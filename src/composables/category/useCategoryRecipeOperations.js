import { watch } from "vue";
import { useNuxtApp } from "#app";
import { useStore } from "vuex";
import { useCategoryStore } from "@/stores/category.js";
import { useSearchStore } from "@/stores/search.js";
import { useTimeUtils } from "@/composables/useTimeUtils.js";
import defaultImage from "@/assets/images/default_recipe_image.png";

export function useCategoryRecipeOperations(state, utils) {
  const { $keys } = useNuxtApp();
  const categoryStore = useCategoryStore();
  const { searchContexts } = useSearchStore();
  const { parseDurationString } = useTimeUtils();
  const { scrollToTop, triggerLoading } = utils;
  const store = useStore();

  const handlePaginationRedirection = async () => {
    const remainingItems = state.recipeDataForCategories.value.length;
    if (remainingItems === 0 && state.fromRecipe.value > 0) {
      state.fromRecipe.value = Math.max(0, state.fromRecipe.value - state.sizeRecipe.value);
      await getRecipeDataForCategoriesAsync(state.categoryISIN.value);
    }
  };

  const handleRecipeAdded = async (addedRecipes) => {
    if (addedRecipes?.length) {
      try {
        let recipesActuallyAdded = false;

        addedRecipes.forEach((recipe) => {
          const existsInRegular = state.recipeDataForCategories.value.find(
            (r) => r.isin === recipe.isin
          );
          const existsInPromoted = state.categoryPromotedRecipes.value.find(
            (r) => r.isin === recipe.isin
          );

          if (!existsInRegular && !existsInPromoted) {
            const removedIndex = state.recipeMatchesIsinsRemove.value.indexOf(recipe.isin);
            if (removedIndex > -1) {
              state.recipeMatchesIsinsRemove.value.splice(removedIndex, 1);
            }

            const formattedRecipe = {
              isin: recipe.isin,
              title: recipe.title?.[state.lang.value] || recipe.title || "",
              totalTime: recipe.time?.total ? parseDurationString(recipe.time.total) : "",
              ingredientCount: recipe.ingredients?.[state.lang.value]?.length || 0,
              image:
                recipe.media?.[state.lang.value]?.image ||
                recipe.media?.[state.lang.value]?.externalImageUrl ||
                defaultImage,
              media: recipe.media,
              time: recipe.time,
              ingredients: recipe.ingredients,
              isPromoted: false,
            };

            state.recipeDataForCategories.value.unshift(formattedRecipe);

            if (!state.selectedCategoryRecipe.value.includes(recipe.isin)) {
              state.selectedCategoryRecipe.value.push(recipe.isin);
            }

            if (!state.addedIsins.value.includes(recipe.isin)) {
              state.addedIsins.value.push(recipe.isin);
            }

            recipesActuallyAdded = true;
          }
        });

        if (recipesActuallyAdded) {
          state.hasChanges.value = true;
          state.recipeForCategoriesTotal.value = state.recipeDataForCategories.value.length;
          state.recipesWereAddedInModal.value = true;
        }
      } catch (error) {
        console.error(
          "[IQ][CategoryForm] Error adding recipes to category:",
          error
        );
      }
    }
  };

  const removeRecipeFromCategory = async () => {
    const index = state.recipeDataForCategories.value.findIndex(
      (r) => r.isin === state.removeRecipeData.value.isin
    );
    if (index > -1) {
      state.recipeDataForCategories.value.splice(index, 1);

      const isinIndex = state.addedIsins.value.indexOf(state.removeRecipeData.value.isin);
      if (isinIndex > -1) {
        state.addedIsins.value.splice(isinIndex, 1);
      }

      if (!state.recipeMatchesIsinsRemove.value.includes(state.removeRecipeData.value.isin)) {
        state.recipeMatchesIsinsRemove.value.push(state.removeRecipeData.value.isin);
      }

      state.hasChanges.value = true;

      if (state.props.isEdit && state.categoryISIN.value) {
        await getRecipeDataForCategoriesAsync(state.categoryISIN.value);
        await handlePaginationRedirection();
      } else {
        state.recipeForCategoriesTotal.value = state.recipeDataForCategories.value.length;
      }
    }
    const selectedCategoryRecipeIndex = state.selectedCategoryRecipe.value.indexOf(state.removeRecipeData.value.isin);
    if (selectedCategoryRecipeIndex > -1) {
      state.selectedCategoryRecipe.value.splice(selectedCategoryRecipeIndex, 1);
    }
  };

  const removePromotedRecipe = async (recipe) => {
    const index = state.categoryPromotedRecipes.value.findIndex(
      (r) => r.isin === recipe.isin
    );
    if (index > -1) {
      let currentPageSelections = [];
      if (state.isSelectionEnabled.value) {
        currentPageSelections = state.recipeDataForCategories.value
          .filter(item => item.isSelectedToDelete)
          .map(item => ({ isin: item.isin, ...item }));
      }

      state.categoryPromotedRecipes.value.splice(index, 1);

      state.categoryPromotedRecipesTotal.value -= 1;
      if (!state.totalPromotedRemovedIsin.value.includes(recipe.isin)) {
        state.totalPromotedRemovedIsin.value.push(recipe.isin);
      }

      state.hasChanges.value = true;
      if (state.props.isEdit && state.categoryISIN.value) {
        await getRecipeDataForCategoriesAsync(state.categoryISIN.value);
        await handlePaginationRedirection();

        if (state.isSelectionEnabled.value && currentPageSelections.length) {
          const currentPageIsins = currentPageSelections.map(item => item.isin);

          state.recipeDataForCategories.value = state.recipeDataForCategories.value.map(recipe => ({
            ...recipe,
            isSelectedToDelete: currentPageIsins.includes(recipe.isin),
          }));

          state.selectedProducts.value = state.recipeDataForCategories.value.filter(recipe => recipe.isSelectedToDelete);
          state.updateSelectionCount();
          checkSelected();
        }
      }
    }
    triggerLoading($keys.KEY_NAMES.RECIPE_UNPROMOTED);
  };

  const promoteRecipe = async (recipe) => {
    const index = state.recipeDataForCategories.value.findIndex(
      (r) => r.isin === recipe.isin
    );
    if (index > -1) {
      let currentPageSelections = [];
      if (state.isSelectionEnabled.value) {
        currentPageSelections = state.recipeDataForCategories.value
          .filter(item => item.isSelectedToDelete && item.isin !== recipe.isin)
          .map(item => ({ isin: item.isin, ...item }));
      }

      const promotedRecipe = { ...recipe, isPromoted: true };
      state.recipeDataForCategories.value.splice(index, 1);
      state.categoryPromotedRecipes.value.push(promotedRecipe);

      state.categoryPromotedRecipesTotal.value += 1;
      state.recipeForCategoriesTotal.value -= 1;

      const removedIndex = state.totalPromotedRemovedIsin.value.indexOf(recipe.isin);
      if (removedIndex > -1) {
        state.totalPromotedRemovedIsin.value.splice(removedIndex, 1);
      }

      state.hasChanges.value = true;

      if (state.props.isEdit && state.categoryISIN.value) {
        await getRecipeDataForCategoriesAsync(state.categoryISIN.value);
        await handlePaginationRedirection();

        if (state.isSelectionEnabled.value && currentPageSelections.length) {
          const currentPageIsins = currentPageSelections.map(item => item.isin);

          state.recipeDataForCategories.value = state.recipeDataForCategories.value.map(recipeItem => ({
            ...recipeItem,
            isSelectedToDelete: currentPageIsins.includes(recipeItem.isin),
          }));

          state.selectedProducts.value = state.recipeDataForCategories.value.filter(recipeItem => recipeItem.isSelectedToDelete);
          state.updateSelectionCount();
          checkSelected();
        }
      }
    }
    scrollToTop();
    triggerLoading($keys.KEY_NAMES.RECIPE_PROMOTED);
  };

  const handleRecipesPageChange = (page) => {
    if (state.isSelectionEnabled.value) {
      const currentPageSelectedItems = state.recipeDataForCategories.value.filter(
        (item) => item.isSelectedToDelete === true
      );
      currentPageSelectedItems.forEach((item) => {
        const exists = state.selectedProductsAcrossPages.value.find((p) => p.isin === item.isin);
        if (!exists) {
          state.selectedProductsAcrossPages.value.push(item);
        }
      });
      state.selectedProducts.value = [];
      state.selectionOfRecipes.value[0].isSelected = false;
    }

    scrollToTop();
    state.fromRecipe.value = (page - 1) * state.sizeRecipe.value;
    const isin = state.props.isEdit
      ? state.categoryISIN.value
      : state.newIsin.value;

    if (isin) {
      getRecipeDataForCategoriesAsync(isin);
    }
  };

  const getPromotedRecipesForCategoriesAsync = async (newIsin) => {
    try {
      if (!newIsin) {
        console.error("[IQ][CategoryForm] Missing required parameter: isin");
        return;
      }

      const lang = store.getters["userData/getDefaultLang"];

      const response = await categoryStore.getPromotedRecipesForCategoriesAsync(newIsin, lang);
      if (response) {
        let promotedRecipes = [];
        if (Array.isArray(response)) {
          promotedRecipes = response.map((recipe) => ({
            ...recipe,
            isPromoted: true,
            totalTime: recipe.time?.total ? parseDurationString(recipe.time.total) : recipe.totalTime || "",
          }));
        } else if (
          response.promotedRecipes &&
          Array.isArray(response.promotedRecipes)
        ) {
          promotedRecipes = response.promotedRecipes.map(
            (recipe) => ({
              ...recipe,
              isPromoted: true,
              totalTime: recipe.time?.total ? parseDurationString(recipe.time.total) : recipe.totalTime || "",
            })
          );
        } else if (response.results && Array.isArray(response.results)) {
          promotedRecipes = response.results.map((recipe) => ({
            ...recipe,
            isPromoted: true,
            totalTime: recipe.time?.total ? parseDurationString(recipe.time.total) : recipe.totalTime || "",
          }));
        } else if (response.data && Array.isArray(response.data)) {
          promotedRecipes = response.data.map((recipe) => ({
            ...recipe,
            isPromoted: true,
            totalTime: recipe.time?.total ? parseDurationString(recipe.time.total) : recipe.totalTime || "",
          }));
        }
        if (state.totalPromotedRemovedIsin.value.length > 0) {
          promotedRecipes = promotedRecipes.filter(
            recipe => !state.totalPromotedRemovedIsin.value.includes(recipe.isin)
          );
        }

        state.categoryPromotedRecipes.value = promotedRecipes;
        state.categoryPromotedRecipesTotal.value = promotedRecipes.length;
      } else {
        state.categoryPromotedRecipes.value = [];
        state.categoryPromotedRecipesTotal.value = 0;
      }
    } catch (error) {
      console.error("[IQ][CategoryForm] Error loading promoted recipes:", error);
      state.categoryPromotedRecipes.value = [];
    }
  };

  const getRecipeDataForCategoriesAsync = async (isin, preserveSearchState = true) => {
    try {
      if (!isin) {
        console.error("[IQ][CategoryForm] Missing required parameter: isin");
        return;
      }

      const lang = store.getters["userData/getDefaultLang"];

      const promotedRecipeIsins = (state.categoryPromotedRecipes.value || []).map(
        (recipe) => recipe?.isin
      ).filter(Boolean);

      const allExcludingIsins = [
        ...promotedRecipeIsins,
        ...state.recipeMatchesIsinsRemove.value,
      ].filter(Boolean);

      const searchQuery = state.getSearchQuery() || "";
      let finalSearchQuery = "";

      if (preserveSearchState) {
        if (searchQuery && searchQuery.trim() !== "") {
          finalSearchQuery = searchQuery.trim();
          state.searchcopy.value = finalSearchQuery;
        } else if (state.searchcopy.value && state.searchcopy.value.trim() !== "") {
          finalSearchQuery = state.searchcopy.value.trim();
        }
      } else {
        finalSearchQuery = searchQuery.trim();
        state.searchcopy.value = finalSearchQuery;
      }

      const payload = {
        country: lang.split("-")[1],
        q: finalSearchQuery,
        excludingIsins: allExcludingIsins.join(","),
        groupsIncludingIsins: state.addedIsins.value.join(","),
        groups: isin,
        from: state.fromRecipe.value,
        size: state.sizeRecipe.value,
        sort: "lastMod",
      };

      const response = await categoryStore.getRecipeForCategoriesAsync(payload);
      if (response?.results && Array.isArray(response.results)) {
        state.recipeDataForCategories.value = response.results.map((recipe) => ({
          ...recipe,
          isPromoted: false,
          isSelectedToDelete: false,
          title: recipe.title || {},
          totalTime: recipe.time?.total ? parseDurationString(recipe.time.total) : "",
          ingredientCount: recipe.ingredients?.[lang]?.length || 0,
          image: recipe.media?.[lang]?.image ||
                 recipe.media?.[lang]?.externalImageUrl ||
                 defaultImage,
        }));
        state.recipeForCategoriesTotal.value = response.total || 0;
        if (state.isSelectionEnabled.value) {
          restorePageSelections();
        }
      } else {
        state.recipeDataForCategories.value = [];
        state.recipeForCategoriesTotal.value = 0;
      }
    } catch (error) {
      console.error(
        "[IQ][CategoryForm] Error loading recipes for categories:",
        error
      );
      state.recipeDataForCategories.value = [];
      state.recipeForCategoriesTotal.value = 0;
    }
  };

  const restorePageSelections = () => {
    if (!state.selectedProductsAcrossPages.value.length) {
      state.selectionOfRecipes.value[0].isSelected = false;
      return;
    }

    const crossPageIsins = state.selectedProductsAcrossPages.value.map(item => item.isin);

    state.recipeDataForCategories.value = state.recipeDataForCategories.value.map(recipe => ({
      ...recipe,
      isSelectedToDelete: crossPageIsins.includes(recipe.isin),
    }));

    state.selectedProducts.value = state.recipeDataForCategories.value.filter(recipe => recipe.isSelectedToDelete);

    const currentPageIsins = state.recipeDataForCategories.value.map(item => item.isin);
    state.selectedProductsAcrossPages.value = state.selectedProductsAcrossPages.value.filter(
      item => !currentPageIsins.includes(item.isin)
    );
    state.updateSelectionCount();
    checkSelected();
  };

  const checkSelected = () => {
    let count = 0;
    state.recipeDataForCategories.value.forEach((item) => {
      if (item.isSelectedToDelete) {
        count += 1;
      }
    });
    state.selectionOfRecipes.value[0].isSelected =
      state.recipeDataForCategories.value.length &&
      count === state.recipeDataForCategories.value.length;
  };

  // Watch for search query changes and refetch recipe data
  watch(
    searchContexts,
    (newSearchQuery, oldSearchQuery) => {
      const newStr = newSearchQuery.detailsPage?.str || "";
      const oldStr = oldSearchQuery.detailsPage?.str || "";

      if (newStr !== oldStr && state.props.isEdit && state.categoryISIN.value) {
        state.searchcopy.value = newStr;
        getRecipeDataForCategoriesAsync(state.categoryISIN.value);
      }
    },
    { immediate: false }
  );

  return {
    handleRecipeAdded,
    removeRecipeFromCategory,
    removePromotedRecipe,
    promoteRecipe,
    handleRecipesPageChange,
    getPromotedRecipesForCategoriesAsync,
    getRecipeDataForCategoriesAsync,
    restorePageSelections,
    checkSelected,
  };
}
